---
import '../../styles/global.css'
import Navigation from '../../components/Navigation.astro'
import Hero from '../../components/Hero.astro'
import Footer from '../../components/Footer.astro'
import Section from '../../components/Section.astro'
import FixedBackground from '../../components/FixedBackground.astro'
import BackgroundOverlay from '../../components/BackgroundOverlay.astro'
import { getTranslations, useTranslations, languages } from '../../i18n/utils.js'
import { getLocalizedMenuData } from '../../data/menu-i18n.ts'
import type { MenuCategory, MenuItem } from '../../types/menu'

// Generate static paths for all languages
export async function getStaticPaths() {
  const paths = Object.keys(languages).map(lang => ({
    params: { lang }
  }));
  
  return paths;
}

// Get the language from the URL params
const { lang } = Astro.params;
const translations = await getTranslations(lang);
const t = useTranslations(translations);
const menuData = getLocalizedMenuData(lang, translations);
---

<html lang={lang}>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width" />
    <meta name="generator" content={Astro.generator} />
    <title>{t('meta.menuTitle')}</title>
    <meta name="description" content={t('meta.menuDescription')} />
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href={`${import.meta.env.BASE_URL}favicon.svg`} />
    <link rel="icon" type="image/png" href={`${import.meta.env.BASE_URL}favicon.png`} />
    <link rel="apple-touch-icon" href={`${import.meta.env.BASE_URL}apple-touch-icon.png`} />
    
    <!-- Visitor tracking -->
    <script>
      // Send visitor data to Google Sheets
      fetch('https://script.google.com/macros/s/AKfycbxvZNXq1f3CU8pEg-pgsij9VICglp62bLKH1l0YRPe4k4GdOFvvLaA0rmNQ2XzqrPRz0Q/exec', {
        method: 'POST',
        mode: 'no-cors',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          timestamp: new Date().toISOString(),
          page: window.location.href,
          userAgent: navigator.userAgent,
          language: navigator.language
        })
      }).catch(err => console.log('Tracking request failed:', err));
    </script>
  </head>

  <body class="bg-white">
    <!-- Fixed Background -->
    <FixedBackground src="drinks.jpeg" />

    <!-- Background Overlay for transparency effects -->
    <BackgroundOverlay />

    <!-- Main Content Container -->
    <div class="z-10 relative">
      <!-- Navigation -->
      <Navigation currentPage="menu" lang={lang} translations={translations} />

      <main>
        <!-- Hero Section -->
        <Hero
          title={t('navigation.menu')}
          subtitle={t('menu.featuredSubtitle')}
          showButtons={false}
          lang={lang}
          translations={translations}
          useFixedBackground={true}
        />

        <!-- Featured Cocktails Section -->
        {menuData.Kokteil && (
          <Section
            background="semi-transparent"
          >
            <div class="gap-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 mx-auto max-w-6xl">
              {menuData.Kokteil.items.map((item: MenuItem) => (
                <div class="bg-white shadow-lg hover:shadow-xl rounded-xl overflow-hidden transition-shadow duration-300">
                  {item.image && (
                    <div class="h-48 overflow-hidden">
                      <img
                        src={`${import.meta.env.BASE_URL}${item.image}`}
                        alt={item.name}
                        class="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                  )}
                  <div class="p-6">
                    <h3 class="font-semibold text-gray-900 text-xl text-center">{item.name}</h3>
                    {item.description && (
                      <p class="mt-3 text-gray-600 text-sm text-center leading-relaxed">{item.description}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </Section>
        )}

        <!-- Full Menu Section -->
        <Section
          title={t('navigation.menu')}
          background="semi-transparent"
        >
          <div class="space-y-12 mx-auto max-w-4xl">
            {Object.entries(menuData).map(([, category]: [string, MenuCategory]) => (
              <div class="bg-white shadow-lg p-8 rounded-xl">
                <h3 class="mb-6 pb-4 border-gray-200 border-b font-bold text-gray-900 text-2xl text-center">
                  {category.title}
                </h3>

                <div class="overflow-x-auto">
                  <table class="w-full border-collapse">
                    <tbody>
                      {category.items.map((item: MenuItem) => (
                        <tr class="border-gray-100 border-b last:border-b-0">
                          <td class="py-3 pr-4 align-top">
                            <h4 class="font-medium text-gray-900">{item.name}</h4>
                            {item.description && (
                              <p class="mt-1 text-gray-600 text-sm">{item.description}</p>
                            )}
                          </td>
                          <td class="py-3 pl-4 text-right align-top">
                            <span class="font-semibold text-blue-600 whitespace-nowrap">{item.price}</span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            ))}
          </div>
        </Section>
      </main>

      <!-- Footer -->
      <Footer lang={lang} translations={translations} />
    </div>
  </body>
</html>
