---
import OptimizedPicture from './OptimizedPicture.astro';
import type { GalleryImage } from '../types/menu';

interface Props {
  images: GalleryImage[];
}

const { images } = Astro.props;
---

<div class="space-y-12">
  <!-- Image Grid -->
  <div class="gap-6 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
    {images.map((image: GalleryImage, index: number) => (
      <figure class="group relative shadow-md hover:shadow-xl rounded-xl aspect-[4/3] overflow-hidden transition-all duration-300">
        <OptimizedPicture
          src={image.src.startsWith(import.meta.env.BASE_URL) ? image.src.replace(import.meta.env.BASE_URL, '') : image.src}
          alt={image.alt}
          loading={index < 2 ? "eager" : "lazy"}
          decoding="async"
          width={400}
          height={300}
          sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw"
          class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          priority={index < 2}
        />
        <div class="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300"></div>
      </figure>
    ))}
  </div>

  <!-- Instagram Section -->
  <aside class="bg-gradient-to-r from-purple-500 to-pink-500 p-4 sm:p-8 rounded-2xl text-white text-center">
    <div class="space-y-6 mx-auto max-w-4xl">
      <h2 class="font-bold text-2xl sm:text-3xl">Follow us on Instagram</h2>
      <p class="opacity-90 text-lg sm:text-xl">@beach_bar_joni</p>

      <!-- Instagram Embed Placeholder -->
      <div class="bg-white/10 backdrop-blur-sm mt-8 p-2 sm:p-4 rounded-lg" id="instagram-container">
        <!-- Loading placeholder -->
        <div id="instagram-placeholder" class="flex justify-center items-center min-h-[400px]">
          <div class="text-center">
            <div class="mx-auto mb-4 border-white border-b-2 rounded-full w-8 h-8 animate-spin"></div>
            <p class="text-white/80">Loading Instagram content...</p>
            <a
              href="https://www.instagram.com/beach_bar_joni/"
              target="_blank"
              class="inline-block bg-white/20 hover:bg-white/30 mt-4 px-6 py-2 rounded-lg transition-colors"
            >
              Visit @beach_bar_joni
            </a>
          </div>
        </div>

        <!-- Instagram embed (loaded dynamically) -->
        <div class="flex justify-center" id="instagram-embed" style="display: none;">
          <blockquote
            class="mx-auto instagram-media"
            data-instgrm-permalink="https://www.instagram.com/beach_bar_joni/"
            data-instgrm-version="12"
            title="Beach Bar Joni Instagram Feed"
            aria-label="Beach Bar Joni Instagram Feed"
            style="background:#FFF; border:0; border-radius:3px; box-shadow:0 0 1px 0 rgba(0,0,0,0.5),0 1px 10px 0 rgba(0,0,0,0.15); margin: 0 auto; max-width:540px; min-width:280px; padding:0; width:calc(100% - 2px);"
          >
          <div style="padding:16px;">
            <a 
              href="https://www.instagram.com/beach_bar_joni/" 
              style="background:#FFFFFF; line-height:0; padding:0 0; text-align:center; text-decoration:none; width:100%;" 
              target="_blank"
            >
              <div style="display: flex; flex-direction: row; align-items: center;">
                <div style="background-color: #F4F4F4; border-radius: 50%; flex-grow: 0; height: 40px; margin-right: 14px; width: 40px;"></div>
                <div style="display: flex; flex-direction: column; flex-grow: 1; justify-content: center;">
                  <div style="background-color: #F4F4F4; border-radius: 4px; flex-grow: 0; height: 14px; margin-bottom: 6px; width: 100px;"></div>
                  <div style="background-color: #F4F4F4; border-radius: 4px; flex-grow: 0; height: 14px; width: 60px;"></div>
                </div>
              </div>
              <div style="padding: 19% 0;"></div>
              <div style="display:block; height:50px; margin:0 auto 12px; width:50px;">
                <svg width="50px" height="50px" viewBox="0 0 60 60" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                    <g transform="translate(-511.000000, -20.000000)" fill="#000000">
                      <g>
                        <path d="M556.869,30.41 C554.814,30.41 553.148,32.076 553.148,34.131 C553.148,36.186 554.814,37.852 556.869,37.852 C558.924,37.852 560.59,36.186 560.59,34.131 C560.59,32.076 558.924,30.41 556.869,30.41 M541,60.657 C535.114,60.657 530.342,55.887 530.342,50 C530.342,44.114 535.114,39.342 541,39.342 C546.887,39.342 551.658,44.114 551.658,50 C551.658,55.887 546.887,60.657 541,60.657 M541,33.886 C532.1,33.886 524.886,41.1 524.886,50 C524.886,58.899 532.1,66.113 541,66.113 C549.9,66.113 557.115,58.899 557.115,50 C557.115,41.1 549.9,33.886 541,33.886 M565.378,62.101 C565.244,65.022 564.756,66.606 564.346,67.663 C563.803,69.06 563.154,70.057 562.106,71.106 C561.058,72.155 560.06,72.803 558.662,73.347 C557.607,73.757 556.021,74.244 553.102,74.378 C549.944,74.521 548.997,74.552 541,74.552 C533.003,74.552 532.056,74.521 528.898,74.378 C525.979,74.244 524.393,73.757 523.338,73.347 C521.94,72.803 520.942,72.155 519.894,71.106 C518.846,70.057 518.197,69.06 517.654,67.663 C517.244,66.606 516.755,65.022 516.623,62.101 C516.479,58.943 516.448,57.996 516.448,50 C516.448,42.003 516.479,41.056 516.623,37.899 C516.755,34.978 517.244,33.391 517.654,32.338 C518.197,30.938 518.846,29.942 519.894,28.894 C520.942,27.846 521.94,27.196 523.338,26.654 C524.393,26.244 525.979,25.756 528.898,25.623 C532.057,25.479 533.004,25.448 541,25.448 C548.997,25.448 549.943,25.479 553.102,25.623 C556.021,25.756 557.607,26.244 558.662,26.654 C560.06,27.196 561.058,27.846 562.106,28.894 C563.154,29.942 563.803,30.938 564.346,32.338 C564.756,33.391 565.244,34.978 565.378,37.899 C565.522,41.056 565.552,42.003 565.552,50 C565.552,57.996 565.522,58.943 565.378,62.101 M570.82,37.631 C570.674,34.438 570.167,32.258 569.425,30.349 C568.659,28.377 567.633,26.702 565.965,25.035 C564.297,23.368 562.623,22.342 560.652,21.575 C558.743,20.834 556.562,20.326 553.369,20.18 C550.169,20.033 549.148,20 541,20 C532.853,20 531.831,20.033 528.631,20.18 C525.438,20.326 523.257,20.834 521.349,21.575 C519.376,22.342 517.703,23.368 516.035,25.035 C514.368,26.702 513.342,28.377 512.574,30.349 C511.834,32.258 511.326,34.438 511.181,37.631 C511.035,40.831 511,41.851 511,50 C511,58.147 511.035,59.17 511.181,62.369 C511.326,65.562 511.834,67.743 512.574,69.651 C513.342,71.625 514.368,73.296 516.035,74.965 C517.703,76.634 519.376,77.658 521.349,78.425 C523.257,79.167 525.438,79.673 528.631,79.82 C531.831,79.965 532.853,80.001 541,80.001 C549.148,80.001 550.169,79.965 553.369,79.82 C556.562,79.673 558.743,79.167 560.652,78.425 C562.623,77.658 564.297,76.634 565.965,74.965 C567.633,73.296 568.659,71.625 569.425,69.651 C570.167,67.743 570.674,65.562 570.82,62.369 C570.966,59.17 571,58.147 571,50 C571,41.851 570.966,40.831 570.82,37.631"></path>
                      </g>
                    </g>
                  </g>
                </svg>
              </div>
              <div style="padding-top: 8px;">
                <div style="color:#3897f0; font-family:Arial,sans-serif; font-size:14px; font-style:normal; font-weight:550; line-height:18px;">View this post on Instagram</div>
              </div>
              <div style="padding: 12.5% 0;"></div>
              <div style="display: flex; flex-direction: row; margin-bottom: 14px; align-items: center;">
                <div>
                  <div style="background-color: #F4F4F4; border-radius: 50%; height: 12.5px; width: 12.5px; transform: translateX(0px) translateY(7px);"></div>
                  <div style="background-color: #F4F4F4; height: 12.5px; transform: rotate(-45deg) translateX(3px) translateY(1px); width: 12.5px; flex-grow: 0; margin-right: 14px; margin-left: 2px;"></div>
                  <div style="background-color: #F4F4F4; border-radius: 50%; height: 12.5px; width: 12.5px; transform: translateX(9px) translateY(-18px);"></div>
                </div>
                <div style="margin-left: 8px;">
                  <div style="background-color: #F4F4F4; border-radius: 50%; flex-grow: 0; height: 20px; width: 20px;"></div>
                  <div style="width: 0; height: 0; border-top: 2px solid transparent; border-left: 6px solid #f4f4f4; border-bottom: 2px solid transparent; transform: translateX(16px) translateY(-4px) rotate(30deg)"></div>
                </div>
                <div style="margin-left: auto;">
                  <div style="width: 0px; border-top: 8px solid #F4F4F4; border-right: 8px solid transparent; transform: translateY(16px);"></div>
                  <div style="background-color: #F4F4F4; flex-grow: 0; height: 12px; width: 16px; transform: translateY(-4px);"></div>
                  <div style="width: 0; height: 0; border-top: 8px solid #F4F4F4; border-left: 8px solid transparent; transform: translateY(-4px) translateX(8px);"></div>
                </div>
              </div>
              <div style="display: flex; flex-direction: column; flex-grow: 1; justify-content: center; margin-bottom: 24px;">
                <div style="background-color: #F4F4F4; border-radius: 4px; flex-grow: 0; height: 14px; margin-bottom: 6px; width: 224px;"></div>
                <div style="background-color: #F4F4F4; border-radius: 4px; flex-grow: 0; height: 14px; width: 144px;"></div>
              </div>
            </a>
            <p style="color:#c9c8cd; font-family:Arial,sans-serif; font-size:14px; line-height:17px; margin-bottom:0; margin-top:8px; overflow:hidden; padding:8px 0 7px; text-align:center; text-overflow:ellipsis; white-space:nowrap;">
              <a href="https://www.instagram.com/beach_bar_joni/" style="color:#c9c8cd; font-family:Arial,sans-serif; font-size:14px; font-style:normal; font-weight:normal; line-height:17px; text-decoration:none;" target="_blank">Beach Bar Joni</a> on <time style="font-family:Arial,sans-serif; font-size:14px; line-height:17px;">Instagram</time>
            </p>
          </div>
        </blockquote>
        </div>
      </div>
    </div>
  </aside>
</div>

<!-- Optimized Instagram loading script -->
<script is:inline>
  // Optimized Instagram embed loading
  function loadInstagramEmbed() {
    const placeholder = document.getElementById('instagram-placeholder');
    const embed = document.getElementById('instagram-embed');

    if (!placeholder || !embed) {
      console.warn('Instagram placeholder or embed container not found');
      return;
    }

    // Show embed, hide placeholder
    placeholder.style.display = 'none';
    embed.style.display = 'flex';

    // Load Instagram script only when needed
    if (!document.querySelector('script[src*="instagram.com/embed.js"]')) {
      const script = document.createElement('script');
      script.async = true;
      script.defer = true;
      script.src = 'https://www.instagram.com/embed.js';
      script.onload = function() {
        // Process Instagram embeds after script loads
        if (window.instgrm && window.instgrm.Embeds) {
          try {
            window.instgrm.Embeds.process();
            // Add title to dynamically created iframe for accessibility
            setTimeout(function() {
              const iframe = document.querySelector('#instagram-embed iframe');
              if (iframe && !iframe.title) {
                iframe.title = 'Beach Bar Joni Instagram Feed';
                iframe.setAttribute('aria-label', 'Beach Bar Joni Instagram Feed');
              }
            }, 1000);
          } catch (error) {
            console.warn('Error processing Instagram embeds:', error);
          }
        }
      };
      script.onerror = function() {
        console.warn('Failed to load Instagram embed script');
        // Show fallback link if script fails to load
        const fallbackLink = embed.querySelector('a[href*="instagram.com"]');
        if (fallbackLink) {
          fallbackLink.style.display = 'block';
        }
      };
      document.head.appendChild(script);
    } else {
      // Script already loaded, just process embeds
      if (window.instgrm && window.instgrm.Embeds) {
        try {
          window.instgrm.Embeds.process();
        } catch (error) {
          console.warn('Error processing existing Instagram embeds:', error);
        }
      }
    }
  }

  // Use Intersection Observer with better performance settings
  document.addEventListener('DOMContentLoaded', function() {
    const instagramContainer = document.getElementById('instagram-container');
    if (instagramContainer) {
      const observer = new IntersectionObserver(function(entries) {
        entries.forEach(function(entry) {
          if (entry.isIntersecting) {
            // Add a small delay to improve perceived performance
            setTimeout(loadInstagramEmbed, 500);
            observer.unobserve(entry.target);
          }
        });
      }, {
        rootMargin: '200px',
        threshold: 0.1
      });

      observer.observe(instagramContainer);
    } else {
      console.warn('Instagram container not found for intersection observer');
    }
  });
</script>

<style>
  /* Ensure Instagram embed is properly centered on mobile */
  .instagram-media {
    margin: 0 auto !important;
  }

  /* Override Instagram's min-width on very small screens */
  @media (max-width: 320px) {
    .instagram-media {
      min-width: 280px !important;
      width: 100% !important;
    }
  }

  /* Ensure proper centering on all screen sizes */
  @media (max-width: 640px) {
    .instagram-media {
      max-width: calc(100vw - 32px) !important;
    }
  }
</style>
