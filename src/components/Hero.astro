---
import { getLocalizedUrl } from '../i18n/utils.ts';
import BackgroundImage from './BackgroundImage.astro';

interface Props {
  title: string;
  subtitle: string;
  backgroundImage?: string; // Made optional since we'll use fixed background
  showButtons?: boolean;
  lang?: string;
  translations?: any;
  useFixedBackground?: boolean;
}

const { title, subtitle, backgroundImage, showButtons = false, lang = 'en', translations, useFixedBackground = false } = Astro.props;

// Get translation function
function t(key: string): string {
  if (!translations) return key;
  const keys = key.split('.');
  let value: any = translations;
  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = value[k];
    } else {
      return key;
    }
  }
  return value;
}
---

{useFixedBackground ? (
  <!-- Hero content for fixed background layout -->
  <div class="relative flex justify-center items-center min-h-screen overflow-hidden">
    <!-- Overlay -->
    <div class="z-10 absolute inset-0 bg-gradient-to-br from-black/30 via-black/20 to-black/30"></div>

    <!-- Content -->
    <section id="home" class="z-20 relative mx-auto px-4 sm:px-6 lg:px-8 max-w-4xl text-center">
      <h1 class="mb-6 font-black text-white text-5xl md:text-6xl lg:text-7xl tracking-tight">
        {title}
      </h1>
      <p class="mx-auto mb-8 max-w-3xl font-light text-white/95 text-xl md:text-2xl lg:text-3xl leading-relaxed">
        {subtitle}
      </p>

      {showButtons && (
        <div class="flex sm:flex-row flex-col justify-center items-center gap-4">
          <a
            href={getLocalizedUrl('/menu', lang)}
            class="inline-flex justify-center items-center bg-teal-600 hover:bg-teal-700 hover:shadow-xl px-8 py-4 rounded-lg min-w-[160px] font-semibold text-white text-lg hover:scale-105 transition-all duration-300"
          >
            {t('hero.viewMenu')}
          </a>
          <a
            href={getLocalizedUrl('/beach-rules', lang)}
            class="inline-flex justify-center items-center bg-blue-600 hover:bg-blue-700 hover:shadow-xl px-8 py-4 rounded-lg min-w-[160px] font-semibold text-white text-lg hover:scale-105 transition-all duration-300"
          >
            {t('hero.beachRules')}
          </a>
          <a
            href="#location"
            class="inline-flex justify-center items-center bg-white/10 hover:bg-white hover:shadow-xl backdrop-blur-sm px-8 py-4 border-2 border-white rounded-lg min-w-[160px] font-semibold text-white hover:text-gray-900 text-lg hover:scale-105 transition-all duration-300"
          >
            {t('hero.findUs')}
          </a>
        </div>
      )}
    </section>
  </div>
) : (
  <!-- Original hero with background image (for menu page, etc.) -->
  <BackgroundImage
    src={backgroundImage || ''}
    class="flex justify-center items-center min-h-screen overflow-hidden"
  >
    <!-- Overlay -->
    <div class="z-10 absolute inset-0 bg-gradient-to-br from-black/30 via-black/20 to-black/30"></div>

    <!-- Content -->
    <section id="home" class="z-20 relative mx-auto px-4 sm:px-6 lg:px-8 max-w-4xl text-center">
      <h1 class="mb-6 font-black text-white text-5xl md:text-6xl lg:text-7xl tracking-tight">
        {title}
      </h1>
      <p class="mx-auto mb-8 max-w-3xl font-light text-white/95 text-xl md:text-2xl lg:text-3xl leading-relaxed">
        {subtitle}
      </p>

      {showButtons && (
        <div class="flex sm:flex-row flex-col justify-center items-center gap-4">
          <a
            href={getLocalizedUrl('/menu', lang)}
            class="inline-flex justify-center items-center bg-teal-600 hover:bg-teal-700 hover:shadow-xl px-8 py-4 rounded-lg min-w-[160px] font-semibold text-white text-lg hover:scale-105 transition-all duration-300"
          >
            {t('hero.viewMenu')}
          </a>
          <a
            href={getLocalizedUrl('/beach-rules', lang)}
            class="inline-flex justify-center items-center bg-blue-600 hover:bg-blue-700 hover:shadow-xl px-8 py-4 rounded-lg min-w-[160px] font-semibold text-white text-lg hover:scale-105 transition-all duration-300"
          >
            {t('hero.beachRules')}
          </a>
          <a
            href="#location"
            class="inline-flex justify-center items-center bg-white/10 hover:bg-white hover:shadow-xl backdrop-blur-sm px-8 py-4 border-2 border-white rounded-lg min-w-[160px] font-semibold text-white hover:text-gray-900 text-lg hover:scale-105 transition-all duration-300"
          >
            {t('hero.findUs')}
          </a>
        </div>
      )}
    </section>
  </BackgroundImage>
)}

<style>
  /* Custom animations */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  section h1 {
    animation: fadeInUp 0.8s ease-out;
    text-shadow: 0 4px 12px rgba(0, 0, 0, 0.7);
  }

  section p {
    animation: fadeInUp 0.8s ease-out 0.2s both;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  }

  section div:last-child {
    animation: fadeInUp 0.8s ease-out 0.4s both;
  }
</style>
