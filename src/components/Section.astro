---
interface Props {
  id?: string;
  title?: string;
  subtitle?: string;
  background?: 'white' | 'gray' | 'dark' | 'transparent' | 'semi-transparent';
  padding?: 'normal' | 'large' | 'small';
  maxWidth?: 'normal' | 'wide' | 'full';
}

const {
  id,
  title,
  subtitle,
  background = 'white',
  padding = 'normal',
  maxWidth = 'normal'
} = Astro.props;

const backgroundClasses = {
  white: 'bg-white',
  gray: 'bg-gray-50',
  dark: 'bg-gray-900 text-white',
  transparent: 'bg-transparent',
  'semi-transparent': 'bg-white/60 backdrop-blur-sm'
};

const paddingClasses = {
  small: 'py-12',
  normal: 'py-16 lg:py-20',
  large: 'py-20 lg:py-24'
};

const maxWidthClasses = {
  normal: 'max-w-7xl',
  wide: 'max-w-8xl',
  full: 'max-w-none'
};
---

<section 
  id={id} 
  class={`${backgroundClasses[background]} ${paddingClasses[padding]}`}
>
  <div class={`${maxWidthClasses[maxWidth]} mx-auto px-4 sm:px-6 lg:px-8`}>
    {(title || subtitle) && (
      <header class="mb-12 lg:mb-16 text-center">
        {title && (
          <h2 class="mb-4 font-bold text-3xl md:text-4xl lg:text-5xl tracking-tight">
            {title}
          </h2>
        )}
        {subtitle && (
          <p class="mx-auto max-w-3xl text-gray-600 text-lg md:text-xl leading-relaxed">
            {subtitle}
          </p>
        )}
      </header>
    )}
    
    <div class="section-content">
      <slot />
    </div>
  </div>
</section>

<style>
  /* Section animations */
  @media (prefers-reduced-motion: no-preference) {
    .section-content > * {
      animation: fadeInUp 0.6s ease-out;
    }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style>
