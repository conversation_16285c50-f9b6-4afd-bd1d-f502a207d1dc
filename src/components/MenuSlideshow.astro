---
export interface Props {
  menuData: Record<string, {
    title: string;
    items: Array<{
      name: string;
      price: string;
    }>;
  }>;
}

const { menuData } = Astro.props;
const categories = Object.entries(menuData);
---

<div class="menu-slideshow">
  <!-- Category Navigation -->
  <div class="menu-nav">
    {categories.map(([key, category], index) => (
      <button 
        class={`menu-nav-btn ${index === 0 ? 'active' : ''}`}
        data-category={key}
        data-index={index}
      >
        {category.title}
      </button>
    ))}
  </div>

  <!-- Slideshow Container -->
  <div class="menu-slides-container">
    <div class="menu-slides">
      {categories.map(([key, category], index) => (
        <div 
          class={`menu-slide ${index === 0 ? 'active' : ''}`}
          data-category={key}
          data-index={index}
        >
          <article class="menu-category">
            <h3>{category.title}</h3>
            <div class="overflow-x-auto">
              <table class="menu-table w-full border-collapse">
                <tbody>
                  {category.items.map((item) => (
                    <tr class="border-gray-100 border-b last:border-b-0 menu-item">
                      <td class="py-3 pr-4 align-top">
                        <h4 class="font-medium text-gray-900 item-name">{item.name}</h4>
                      </td>
                      <td class="py-3 pl-4 text-right align-top">
                        <span class="font-semibold text-blue-600 whitespace-nowrap item-price">{item.price}</span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </article>
        </div>
      ))}
    </div>
  </div>

  <!-- Navigation Arrows -->
  <div class="menu-arrows">
    <button class="menu-arrow menu-arrow-prev" aria-label="Previous category">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <polyline points="15,18 9,12 15,6"></polyline>
      </svg>
    </button>
    <button class="menu-arrow menu-arrow-next" aria-label="Next category">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <polyline points="9,18 15,12 9,6"></polyline>
      </svg>
    </button>
  </div>

  <!-- Dots Indicator -->
  <div class="menu-dots">
    {categories.map((_, index) => (
      <button 
        class={`menu-dot ${index === 0 ? 'active' : ''}`}
        data-index={index}
        aria-label={`Go to category ${index + 1}`}
      ></button>
    ))}
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const slideshow = document.querySelector('.menu-slideshow');
    if (!slideshow) return;

    const slides = slideshow.querySelectorAll('.menu-slide');
    const navBtns = slideshow.querySelectorAll('.menu-nav-btn');
    const dots = slideshow.querySelectorAll('.menu-dot');
    const prevBtn = slideshow.querySelector('.menu-arrow-prev');
    const nextBtn = slideshow.querySelector('.menu-arrow-next');
    
    let currentSlide = 0;
    const totalSlides = slides.length;

    function showSlide(index) {
      // Remove active class from all elements
      slides.forEach(slide => slide.classList.remove('active'));
      navBtns.forEach(btn => btn.classList.remove('active'));
      dots.forEach(dot => dot.classList.remove('active'));

      // Add active class to current elements
      slides[index].classList.add('active');
      navBtns[index].classList.add('active');
      dots[index].classList.add('active');

      currentSlide = index;
    }

    function nextSlide() {
      const next = (currentSlide + 1) % totalSlides;
      showSlide(next);
    }

    function prevSlide() {
      const prev = (currentSlide - 1 + totalSlides) % totalSlides;
      showSlide(prev);
    }

    // Navigation button clicks
    navBtns.forEach((btn, index) => {
      btn.addEventListener('click', () => showSlide(index));
    });

    // Dot clicks
    dots.forEach((dot, index) => {
      dot.addEventListener('click', () => showSlide(index));
    });

    // Arrow clicks
    if (prevBtn) prevBtn.addEventListener('click', prevSlide);
    if (nextBtn) nextBtn.addEventListener('click', nextSlide);

    // Auto-advance slideshow (optional)
    let autoSlideInterval = setInterval(nextSlide, 8000);

    // Pause auto-advance on hover
    slideshow.addEventListener('mouseenter', () => {
      clearInterval(autoSlideInterval);
    });

    slideshow.addEventListener('mouseleave', () => {
      autoSlideInterval = setInterval(nextSlide, 8000);
    });

    // Touch/swipe support for mobile
    let startX = 0;
    let endX = 0;

    slideshow.addEventListener('touchstart', (e) => {
      startX = e.touches[0].clientX;
    });

    slideshow.addEventListener('touchend', (e) => {
      endX = e.changedTouches[0].clientX;
      const diff = startX - endX;
      
      if (Math.abs(diff) > 50) { // Minimum swipe distance
        if (diff > 0) {
          nextSlide();
        } else {
          prevSlide();
        }
      }
    });
  });
</script>
