---
interface Props {
  src: string;
  class?: string;
  children?: any;
}

const {
  src,
  class: className = '',
  children
} = Astro.props;

// Generate WebP source path
const webpSrc = src ? src.replace(/\.(jpe?g|png)$/i, '.webp') : '';
const baseUrl = import.meta.env.BASE_URL;

// Create full URLs
const webpUrl = baseUrl + webpSrc;
const fallbackUrl = baseUrl + src;
---

<div class={`relative ${className}`}>
  <!-- WebP background for modern browsers -->
  <div
    class="absolute inset-0 bg-cover bg-center bg-fixed webp-bg"
    style={`background-image: url('${webpUrl}'); background-size: cover; background-position: center;`}
  ></div>

  <!-- Fallback JPEG/PNG background -->
  <div
    class="absolute inset-0 bg-cover bg-center bg-fixed fallback-bg"
    style={`background-image: url('${fallbackUrl}'); background-size: cover; background-position: center;`}
  ></div>

  <!-- Content slot - no wrapper div -->
  <slot />
</div>

<style>
  /* Show WebP by default, hide fallback */
  .webp-bg {
    opacity: 1;
  }
  .fallback-bg {
    opacity: 0;
  }
  
  /* If WebP is not supported, show fallback */
  .no-webp .webp-bg {
    opacity: 0;
  }
  .no-webp .fallback-bg {
    opacity: 1;
  }
</style>

<script>
  // Detect WebP support
  function supportsWebP() {
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
  }
  
  // Add appropriate class to document
  if (supportsWebP()) {
    document.documentElement.classList.add('webp');
  } else {
    document.documentElement.classList.add('no-webp');
  }
</script>
