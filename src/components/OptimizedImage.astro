---
interface Props {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  class?: string;
  loading?: 'lazy' | 'eager';
  sizes?: string;
  decoding?: 'async' | 'sync' | 'auto';
}

const {
  src,
  alt,
  width,
  height,
  class: className = '',
  loading = 'lazy',
  sizes,
  decoding = 'async'
} = Astro.props;
---

<img
  src={src}
  alt={alt}
  width={width}
  height={height}
  class={className}
  loading={loading}
  sizes={sizes}
  decoding={decoding}
/>
