---
interface Props {
  href?: string;
  variant?: 'primary' | 'secondary' | 'outline' | 'gradient';
  size?: 'sm' | 'md' | 'lg';
  class?: string;
  target?: string;
  rel?: string;
}

const { 
  href, 
  variant = 'primary', 
  size = 'md', 
  class: className = '',
  target,
  rel
} = Astro.props;

const baseClasses = 'inline-flex items-center justify-center font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 rounded-lg';

const variantClasses = {
  primary: 'bg-teal-600 hover:bg-teal-700 text-white focus:ring-teal-500',
  secondary: 'bg-slate-600 hover:bg-slate-700 text-white focus:ring-slate-500',
  outline: 'border-2 border-teal-600 text-teal-600 hover:bg-teal-600 hover:text-white focus:ring-teal-500',
  gradient: 'bg-gradient-to-r from-teal-600 to-cyan-500 hover:from-teal-700 hover:to-cyan-600 text-white focus:ring-teal-500'
};

const sizeClasses = {
  sm: 'px-4 py-2 text-sm',
  md: 'px-6 py-3 text-base',
  lg: 'px-8 py-4 text-lg'
};

const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;

const Element = href ? 'a' : 'button';
---

<Element 
  href={href}
  class={classes}
  target={target}
  rel={rel}
>
  <slot />
</Element>
